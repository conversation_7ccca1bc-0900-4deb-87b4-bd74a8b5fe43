import React, { useState, useEffect } from 'react';
import Layout from './Layout';
import { 
  FaUsers, 
  FaBox, 
  FaReceipt, 
  FaUserShield,
  FaArrowUp,
  FaArrowDown,
  FaEye
} from 'react-icons/fa';
import { userAPI, productAPI, transactionAPI, adminAPI } from '../services/api';
import { toast } from 'react-toastify';

const Dashboard = () => {
  const [stats, setStats] = useState({
    users: { total: 0, change: 0 },
    products: { total: 0, change: 0 },
    transactions: { total: 0, change: 0 },
    admins: { total: 0, change: 0 }
  });
  const [recentTransactions, setRecentTransactions] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch all stats in parallel
      const [usersRes, productsRes, transactionsRes, adminsRes] = await Promise.allSettled([
        userAPI.getUserStats(),
        productAPI.getAllProducts({ limit: 1 }),
        transactionAPI.getTransactionStats(),
        adminAPI.getAllAdmins()
      ]);

      // Process results
      const newStats = { ...stats };

      console.log('API Responses:', {
        users: usersRes.status === 'fulfilled' ? usersRes.value.data : usersRes.reason,
        products: productsRes.status === 'fulfilled' ? productsRes.value.data : productsRes.reason,
        transactions: transactionsRes.status === 'fulfilled' ? transactionsRes.value.data : transactionsRes.reason,
        admins: adminsRes.status === 'fulfilled' ? adminsRes.value.data : adminsRes.reason
      });

      if (usersRes.status === 'fulfilled') {
        newStats.users = {
          total: usersRes.value.data.totalUsers || 0,
          change: usersRes.value.data.monthlyGrowth || 0
        };
        console.log('Users stats:', newStats.users);
      }

      if (productsRes.status === 'fulfilled') {
        newStats.products = {
          total: productsRes.value.data.totalProducts || 0,
          change: 0 // You can implement product growth tracking
        };
        console.log('Products stats:', newStats.products);
      }

      if (transactionsRes.status === 'fulfilled') {
        newStats.transactions = {
          total: transactionsRes.value.data.stats?.totalTransactions || 0,
          change: transactionsRes.value.data.monthlyGrowth || 0
        };
        console.log('Transactions stats:', newStats.transactions);
      }

      if (adminsRes.status === 'fulfilled') {
        newStats.admins = {
          total: adminsRes.value.data.admins?.length || 0,
          change: 0
        };
        console.log('Admins stats:', newStats.admins);
      }

      setStats(newStats);

      // Fetch recent transactions
      try {
        const recentRes = await transactionAPI.getAllTransactions({ 
          limit: 5, 
          sort: 'createdAt',
          order: 'desc' 
        });
        setRecentTransactions(recentRes.data.transactions || []);
      } catch (error) {
        console.error('Error fetching recent transactions:', error);
      }

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const StatCard = ({ title, value, change, icon: Icon, color, link }) => (
    <div className="stat-card">
      <div className="stat-header">
        <div className="stat-info">
          <h3 className="stat-title">{title}</h3>
          <p className="stat-value">{loading ? '...' : value.toLocaleString()}</p>
        </div>
        <div className="stat-icon" style={{ backgroundColor: color }}>
          <Icon />
        </div>
      </div>
      <div className="stat-footer">
        <div className={`stat-change ${change >= 0 ? 'positive' : 'negative'}`}>
          {change >= 0 ? <FaArrowUp /> : <FaArrowDown />}
          <span>{Math.abs(change)}%</span>
        </div>
        <span className="stat-period">vs last month</span>
      </div>
    </div>
  );

  if (loading) {
    return (
      <Layout>
        <div className="loading">Loading dashboard...</div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="dashboard">
        {/* Stats Grid */}
        <div className="stats-grid">
          <StatCard
            title="Total Users"
            value={stats.users.total}
            change={stats.users.change}
            icon={FaUsers}
            color="#007bff"
          />
          <StatCard
            title="Total Products"
            value={stats.products.total}
            change={stats.products.change}
            icon={FaBox}
            color="#ffc107"
          />
          <StatCard
            title="Total Transactions"
            value={stats.transactions.total}
            change={stats.transactions.change}
            icon={FaReceipt}
            color="#6f42c1"
          />
          <StatCard
            title="Total Admins"
            value={stats.admins.total}
            change={stats.admins.change}
            icon={FaUserShield}
            color="#28a745"
          />
        </div>

        {/* Recent Transactions */}
        <div className="dashboard-section">
          <div className="card">
            <div className="card-header">
              <h2 className="card-title">Recent Transactions</h2>
              <a href="/transactions" className="btn btn-primary btn-sm">
                <FaEye /> View All
              </a>
            </div>
            
            {recentTransactions.length > 0 ? (
              <div className="table-responsive">
                <table className="table">
                  <thead>
                    <tr>
                      <th>Transaction ID</th>
                      <th>User</th>
                      <th>Amount</th>
                      <th>Status</th>
                      <th>Date</th>
                    </tr>
                  </thead>
                  <tbody>
                    {recentTransactions.map((transaction) => (
                      <tr key={transaction.id}>
                        <td>#{transaction.id}</td>
                        <td>{transaction.userName || 'N/A'}</td>
                        <td>${transaction.amount?.toFixed(2) || '0.00'}</td>
                        <td>
                          <span className={`status-badge ${transaction.status?.toLowerCase()}`}>
                            {transaction.status || 'Pending'}
                          </span>
                        </td>
                        <td>{new Date(transaction.createdAt).toLocaleDateString()}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="empty-state">
                <FaReceipt />
                <p>No recent transactions found</p>
              </div>
            )}
          </div>
        </div>
      </div>

      <style jsx>{`
        .dashboard {
          max-width: 1200px;
          margin: 0 auto;
        }

        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 20px;
          margin-bottom: 30px;
        }

        .stat-card {
          background: white;
          border-radius: 12px;
          padding: 24px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .stat-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .stat-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 16px;
        }

        .stat-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
          font-weight: 500;
        }

        .stat-value {
          font-size: 32px;
          font-weight: 700;
          color: #333;
          margin: 0;
        }

        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 20px;
        }

        .stat-footer {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        .stat-change {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 14px;
          font-weight: 600;
        }

        .stat-change.positive {
          color: #28a745;
        }

        .stat-change.negative {
          color: #dc3545;
        }

        .stat-period {
          font-size: 12px;
          color: #666;
        }

        .dashboard-section {
          margin-bottom: 30px;
        }

        .table-responsive {
          overflow-x: auto;
        }

        .status-badge {
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
          text-transform: capitalize;
        }

        .status-badge.completed {
          background-color: #d4edda;
          color: #155724;
        }

        .status-badge.pending {
          background-color: #fff3cd;
          color: #856404;
        }

        .status-badge.failed {
          background-color: #f8d7da;
          color: #721c24;
        }

        .empty-state {
          text-align: center;
          padding: 40px;
          color: #666;
        }

        .empty-state svg {
          font-size: 48px;
          margin-bottom: 16px;
          opacity: 0.5;
        }

        @media (max-width: 768px) {
          .stats-grid {
            grid-template-columns: 1fr;
          }
          
          .stat-value {
            font-size: 24px;
          }
        }
      `}</style>
    </Layout>
  );
};

export default Dashboard;
