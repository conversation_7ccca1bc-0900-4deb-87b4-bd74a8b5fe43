[{"D:\\Node-project\\admin-dashboard\\src\\index.js": "1", "D:\\Node-project\\admin-dashboard\\src\\App.js": "2", "D:\\Node-project\\admin-dashboard\\src\\components\\TransactionManagement.js": "3", "D:\\Node-project\\admin-dashboard\\src\\components\\UserManagement.js": "4", "D:\\Node-project\\admin-dashboard\\src\\components\\Login.js": "5", "D:\\Node-project\\admin-dashboard\\src\\components\\AdminManagement.js": "6", "D:\\Node-project\\admin-dashboard\\src\\components\\ProductManagement.js": "7", "D:\\Node-project\\admin-dashboard\\src\\components\\Dashboard.js": "8", "D:\\Node-project\\admin-dashboard\\src\\contexts\\AuthContext.js": "9", "D:\\Node-project\\admin-dashboard\\src\\components\\Layout.js": "10", "D:\\Node-project\\admin-dashboard\\src\\services\\api.js": "11"}, {"size": 254, "mtime": 1752434290088, "results": "12", "hashOfConfig": "13"}, {"size": 3352, "mtime": 1752434330359, "results": "14", "hashOfConfig": "13"}, {"size": 20629, "mtime": 1752434733982, "results": "15", "hashOfConfig": "13"}, {"size": 15259, "mtime": 1752437723029, "results": "16", "hashOfConfig": "13"}, {"size": 3990, "mtime": 1752437049040, "results": "17", "hashOfConfig": "13"}, {"size": 11322, "mtime": 1752435259835, "results": "18", "hashOfConfig": "13"}, {"size": 16784, "mtime": 1752434608160, "results": "19", "hashOfConfig": "13"}, {"size": 9994, "mtime": 1752436924345, "results": "20", "hashOfConfig": "13"}, {"size": 2978, "mtime": 1752434349338, "results": "21", "hashOfConfig": "13"}, {"size": 3938, "mtime": 1752434618755, "results": "22", "hashOfConfig": "13"}, {"size": 3330, "mtime": 1752434366517, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "18m5174", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Node-project\\admin-dashboard\\src\\index.js", [], [], "D:\\Node-project\\admin-dashboard\\src\\App.js", [], [], "D:\\Node-project\\admin-dashboard\\src\\components\\TransactionManagement.js", ["57"], [], "D:\\Node-project\\admin-dashboard\\src\\components\\UserManagement.js", ["58"], [], "D:\\Node-project\\admin-dashboard\\src\\components\\Login.js", [], [], "D:\\Node-project\\admin-dashboard\\src\\components\\AdminManagement.js", [], [], "D:\\Node-project\\admin-dashboard\\src\\components\\ProductManagement.js", ["59"], [], "D:\\Node-project\\admin-dashboard\\src\\components\\Dashboard.js", ["60"], [], "D:\\Node-project\\admin-dashboard\\src\\contexts\\AuthContext.js", [], [], "D:\\Node-project\\admin-dashboard\\src\\components\\Layout.js", [], [], "D:\\Node-project\\admin-dashboard\\src\\services\\api.js", [], [], {"ruleId": "61", "severity": 1, "message": "62", "line": 33, "column": 6, "nodeType": "63", "endLine": 33, "endColumn": 40, "suggestions": "64"}, {"ruleId": "61", "severity": 1, "message": "65", "line": 24, "column": 6, "nodeType": "63", "endLine": 24, "endColumn": 31, "suggestions": "66"}, {"ruleId": "61", "severity": 1, "message": "67", "line": 29, "column": 6, "nodeType": "63", "endLine": 29, "endColumn": 31, "suggestions": "68"}, {"ruleId": "61", "severity": 1, "message": "69", "line": 27, "column": 6, "nodeType": "63", "endLine": 27, "endColumn": 8, "suggestions": "70"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchTransactions'. Either include it or remove the dependency array.", "ArrayExpression", ["71"], "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["72"], "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", ["73"], "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", ["74"], {"desc": "75", "fix": "76"}, {"desc": "77", "fix": "78"}, {"desc": "79", "fix": "80"}, {"desc": "81", "fix": "82"}, "Update the dependencies array to be: [currentPage, searchTerm, filters, fetchTransactions]", {"range": "83", "text": "84"}, "Update the dependencies array to be: [currentPage, fetchUsers, searchTerm]", {"range": "85", "text": "86"}, "Update the dependencies array to be: [currentPage, fetchProducts, searchTerm]", {"range": "87", "text": "88"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "89", "text": "90"}, [1101, 1135], "[currentPage, searchTerm, filters, fetchTransactions]", [798, 823], "[currentPage, fetchUsers, searchTerm]", [925, 950], "[currentPage, fetchProducts, searchTerm]", [721, 723], "[fetchDashboardData]"]