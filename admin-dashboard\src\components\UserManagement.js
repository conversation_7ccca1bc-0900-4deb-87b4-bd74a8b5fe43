import React, { useState, useEffect } from 'react';
import Layout from './Layout';
import { FaTrash, FaSearch, <PERSON>aEye, FaUsers } from 'react-icons/fa';
import { userAPI } from '../services/api';
import { toast } from 'react-toastify';

const UserManagement = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedUser, setSelectedUser] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    newUsersThisMonth: 0
  });

  useEffect(() => {
    fetchUsers();
    fetchUserStats();
  }, [currentPage, searchTerm]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        limit: 10,
        search: searchTerm
      };
      
      const response = await userAPI.getAllUsers(params);
      setUsers(response.data.users || []);
      setTotalPages(response.data.totalPages || 1);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('Failed to fetch users');
    } finally {
      setLoading(false);
    }
  };

  const fetchUserStats = async () => {
    try {
      const response = await userAPI.getUserStats();
      setStats(response.data.stats || {
        totalUsers: 0,
        activeUsers: 0,
        newUsersThisMonth: 0
      });
    } catch (error) {
      console.error('Error fetching user stats:', error);
    }
  };

  const handleDelete = async (userId) => {
    if (!window.confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      return;
    }

    try {
      await userAPI.deleteUser(userId);
      toast.success('User deleted successfully');
      fetchUsers();
      fetchUserStats();
    } catch (error) {
      console.error('Error deleting user:', error);
      toast.error('Failed to delete user');
    }
  };

  const handleViewUser = async (userId) => {
    try {
      const response = await userAPI.getUserById(userId);
      setSelectedUser(response.data.user);
      setShowModal(true);
    } catch (error) {
      console.error('Error fetching user details:', error);
      toast.error('Failed to fetch user details');
    }
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedUser(null);
  };

  const StatCard = ({ title, value, icon: Icon, color }) => (
    <div className="stat-card">
      <div className="stat-content">
        <div className="stat-info">
          <h3>{title}</h3>
          <p>{(value || 0).toLocaleString()}</p>
        </div>
        <div className="stat-icon" style={{ backgroundColor: color }}>
          <Icon />
        </div>
      </div>
    </div>
  );

  return (
    <Layout>
      <div className="user-management">
        <div className="page-header">
          <h1>User Management</h1>
        </div>

        {/* Stats Cards */}
        <div className="stats-grid">
          <StatCard
            title="Total Users"
            value={stats.totalUsers}
            icon={FaUsers}
            color="#007bff"
          />
          <StatCard
            title="Active Users"
            value={stats.activeUsers}
            icon={FaUsers}
            color="#28a745"
          />
          <StatCard
            title="New This Month"
            value={stats.newUsersThisMonth}
            icon={FaUsers}
            color="#17a2b8"
          />
        </div>

        {/* Search Bar */}
        <div className="search-bar">
          <div className="search-input">
            <FaSearch className="search-icon" />
            <input
              type="text"
              placeholder="Search users by name, email, or username..."
              value={searchTerm}
              onChange={handleSearch}
              className="form-control"
            />
          </div>
        </div>

        {loading ? (
          <div className="loading">Loading users...</div>
        ) : (
          <>
            <div className="card">
              <div className="table-responsive">
                <table className="table">
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Username</th>
                      <th>Email</th>
                      <th>Phone</th>
                      <th>Gender</th>
                      <th>Joined Date</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {users.map((user) => (
                      <tr key={user._id || user.id}>
                        <td>{user.name || 'N/A'}</td>
                        <td>{user.username || 'N/A'}</td>
                        <td>{user.email || 'N/A'}</td>
                        <td>{user.phone || 'N/A'}</td>
                        <td className="capitalize">{user.Gender || user.gender || 'N/A'}</td>
                        <td>{user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}</td>
                        <td>
                          <div className="action-buttons">
                            <button
                              className="btn btn-primary btn-sm"
                              onClick={() => handleViewUser(user._id || user.id)}
                              title="View Details"
                            >
                              <FaEye />
                            </button>
                            <button
                              className="btn btn-danger btn-sm"
                              onClick={() => handleDelete(user._id || user.id)}
                              title="Delete User"
                            >
                              <FaTrash />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                
                {users.length === 0 && (
                  <div className="empty-state">
                    <FaUsers />
                    <p>No users found</p>
                  </div>
                )}
              </div>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="pagination">
                <button
                  className="btn btn-secondary"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </button>
                <span className="page-info">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  className="btn btn-secondary"
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </button>
              </div>
            )}
          </>
        )}

        {/* User Details Modal */}
        {showModal && selectedUser && (
          <div className="modal-overlay" onClick={closeModal}>
            <div className="modal" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h2>User Details</h2>
                <button className="modal-close" onClick={closeModal}>×</button>
              </div>
              
              <div className="modal-body">
                <div className="user-details">
                  <div className="detail-row">
                    <label>Name:</label>
                    <span>{selectedUser.name || 'N/A'}</span>
                  </div>
                  <div className="detail-row">
                    <label>Username:</label>
                    <span>{selectedUser.username || 'N/A'}</span>
                  </div>
                  <div className="detail-row">
                    <label>Email:</label>
                    <span>{selectedUser.email || 'N/A'}</span>
                  </div>
                  <div className="detail-row">
                    <label>Phone:</label>
                    <span>{selectedUser.phone || 'N/A'}</span>
                  </div>
                  <div className="detail-row">
                    <label>Gender:</label>
                    <span className="capitalize">{selectedUser.Gender || selectedUser.gender || 'N/A'}</span>
                  </div>
                  <div className="detail-row">
                    <label>Date of Birth:</label>
                    <span>{selectedUser.DOB ? new Date(selectedUser.DOB).toLocaleDateString() : 'N/A'}</span>
                  </div>
                  <div className="detail-row">
                    <label>Address:</label>
                    <span>{selectedUser.Address || 'N/A'}</span>
                  </div>
                  <div className="detail-row">
                    <label>Communication Type:</label>
                    <span className="capitalize">{selectedUser.communicationType || 'N/A'}</span>
                  </div>
                  <div className="detail-row">
                    <label>Reference Number:</label>
                    <span>{selectedUser.referenceNumber || 'N/A'}</span>
                  </div>
                  <div className="detail-row">
                    <label>Referred By:</label>
                    <span>{selectedUser.referredBy || 'N/A'}</span>
                  </div>
                  <div className="detail-row">
                    <label>Referral Level:</label>
                    <span>{selectedUser.referralLevel || 0}</span>
                  </div>
                  <div className="detail-row">
                    <label>Total Referrals:</label>
                    <span>{selectedUser.totalReferrals || 0}</span>
                  </div>
                  <div className="detail-row">
                    <label>Joined Date:</label>
                    <span>{selectedUser.createdAt ? new Date(selectedUser.createdAt).toLocaleString() : 'N/A'}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      <style jsx>{`
        .user-management {
          max-width: 1200px;
          margin: 0 auto;
        }

        .page-header {
          margin-bottom: 30px;
        }

        .page-header h1 {
          margin: 0;
          color: #333;
        }

        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 20px;
          margin-bottom: 30px;
        }

        .stat-card {
          background: white;
          border-radius: 12px;
          padding: 20px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .stat-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .stat-info h3 {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
          font-weight: 500;
        }

        .stat-info p {
          font-size: 24px;
          font-weight: 700;
          color: #333;
          margin: 0;
        }

        .stat-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 18px;
        }

        .search-bar {
          margin-bottom: 20px;
        }

        .search-input {
          position: relative;
          max-width: 500px;
        }

        .search-icon {
          position: absolute;
          left: 12px;
          top: 50%;
          transform: translateY(-50%);
          color: #666;
          font-size: 16px;
        }

        .search-input .form-control {
          padding-left: 40px;
        }

        .action-buttons {
          display: flex;
          gap: 8px;
        }

        .capitalize {
          text-transform: capitalize;
        }

        .empty-state {
          text-align: center;
          padding: 40px;
          color: #666;
        }

        .empty-state svg {
          font-size: 48px;
          margin-bottom: 16px;
          opacity: 0.5;
        }

        .pagination {
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 20px;
          margin-top: 20px;
        }

        .page-info {
          font-size: 14px;
          color: #666;
        }

        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }

        .modal {
          background: white;
          border-radius: 12px;
          width: 90%;
          max-width: 600px;
          max-height: 90vh;
          overflow-y: auto;
        }

        .modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20px;
          border-bottom: 1px solid #dee2e6;
        }

        .modal-header h2 {
          margin: 0;
          color: #333;
        }

        .modal-close {
          background: none;
          border: none;
          font-size: 24px;
          color: #666;
          cursor: pointer;
          padding: 0;
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .modal-body {
          padding: 20px;
        }

        .user-details {
          display: grid;
          gap: 15px;
        }

        .detail-row {
          display: grid;
          grid-template-columns: 150px 1fr;
          gap: 15px;
          align-items: center;
          padding: 10px 0;
          border-bottom: 1px solid #f0f0f0;
        }

        .detail-row:last-child {
          border-bottom: none;
        }

        .detail-row label {
          font-weight: 600;
          color: #333;
        }

        .detail-row span {
          color: #666;
        }

        @media (max-width: 768px) {
          .stats-grid {
            grid-template-columns: 1fr;
          }

          .modal {
            width: 95%;
            margin: 20px;
          }

          .detail-row {
            grid-template-columns: 1fr;
            gap: 5px;
          }
        }
      `}</style>
    </Layout>
  );
};

export default UserManagement;
