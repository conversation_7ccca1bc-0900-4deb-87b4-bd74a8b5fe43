{"name": "node-project", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/MostafaGadoo/Node-project.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/MostafaGadoo/Node-project/issues"}, "homepage": "https://github.com/MostafaGadoo/Node-project#readme", "description": "", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^5.1.0", "mongodb": "^6.17.0", "mongoose": "^8.16.2", "morgan": "^1.10.0"}}