.layout {
  display: flex;
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* Sidebar Styles */
.sidebar {
  width: 280px;
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.sidebar-open {
  transform: translateX(0);
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-header h2 {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.sidebar-close {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.sidebar-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-nav {
  flex: 1;
  padding: 20px 0;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.nav-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border-left-color: var(--item-color, #007bff);
}

.nav-item.active {
  background-color: rgba(255, 255, 255, 0.15);
  color: white;
  border-left-color: var(--item-color, #007bff);
}

.nav-icon {
  font-size: 18px;
  margin-right: 12px;
  color: var(--item-color, #007bff);
}

.nav-label {
  font-size: 14px;
  font-weight: 500;
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-info {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.admin-avatar {
  font-size: 24px;
  margin-right: 12px;
  color: #007bff;
}

.admin-details {
  display: flex;
  flex-direction: column;
}

.admin-name {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 2px;
}

.admin-email {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.logout-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.logout-btn:hover {
  background-color: #c0392b;
}

.logout-btn svg {
  margin-right: 8px;
}

/* Main Content */
.main-content {
  flex: 1;
  margin-left: 0;
  display: flex;
  flex-direction: column;
}

/* Header */
.header {
  background: white;
  padding: 0 20px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.menu-toggle {
  background: none;
  border: none;
  font-size: 20px;
  color: #333;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.menu-toggle:hover {
  background-color: #f8f9fa;
}

.header-title h1 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.admin-profile {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 20px;
}

.profile-icon {
  font-size: 16px;
  color: #007bff;
}

.profile-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.logout-btn-header {
  background: none;
  border: none;
  font-size: 18px;
  color: #666;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.logout-btn-header:hover {
  background-color: #f8f9fa;
  color: #e74c3c;
}

/* Page Content */
.page-content {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
}

/* Sidebar Overlay */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

/* Desktop Styles */
@media (min-width: 1024px) {
  .sidebar {
    position: relative;
    transform: translateX(0);
  }
  
  .sidebar-close {
    display: none;
  }
  
  .menu-toggle {
    display: none;
  }
  
  .sidebar-overlay {
    display: none;
  }
  
  .main-content {
    margin-left: 280px;
  }
}

/* Tablet Styles */
@media (max-width: 1023px) and (min-width: 768px) {
  .header-title h1 {
    font-size: 20px;
  }
  
  .admin-profile .profile-name {
    display: none;
  }
}

/* Mobile Styles */
@media (max-width: 767px) {
  .page-content {
    padding: 20px 15px;
  }
  
  .header {
    padding: 0 15px;
  }
  
  .header-title h1 {
    font-size: 18px;
  }
  
  .admin-profile {
    display: none;
  }
  
  .sidebar {
    width: 100%;
  }
}
