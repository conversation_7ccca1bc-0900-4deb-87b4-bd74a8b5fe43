{"version": 3, "file": "SetLike.mjs", "names": ["SetLike", "items", "value", "has", "push", "<PERSON><PERSON><PERSON><PERSON>", "length", "filter", "item", "callbackfn", "for<PERSON>ach", "indexOf", "Set"], "sources": ["../../sources/polyfills/SetLike.ts"], "sourcesContent": ["declare global {\n\tclass Set<T> {\n\t\t// es2015.collection.d.ts\n\t\tconstructor(items?: T[]);\n\t\tadd(value: T): this;\n\t\tclear(): void;\n\t\tdelete(value: T): boolean;\n\t\tforEach(\n\t\t\tcallbackfn: (value: T, value2: T, set: Set<T>) => void,\n\t\t\tthisArg?: unknown\n\t\t): void;\n\t\thas(value: T): boolean;\n\t\treadonly size: number;\n\n\t\t// es2015.iterable.d.ts\n\t\t// no  implemennted\n\t}\n}\n\n// for environments without Set we fallback to arrays with unique members\nclass SetLike<T> implements Set<T> {\n\tprivate items: T[];\n\n\tconstructor(items: T[] = []) {\n\t\tthis.items = items;\n\t}\n\n\tadd(value: T): this {\n\t\tif (this.has(value) === false) {\n\t\t\tthis.items.push(value);\n\t\t}\n\t\treturn this;\n\t}\n\tclear(): void {\n\t\tthis.items = [];\n\t}\n\tdelete(value: T): boolean {\n\t\tconst previousLength = this.items.length;\n\t\tthis.items = this.items.filter((item) => item !== value);\n\n\t\treturn previousLength !== this.items.length;\n\t}\n\tforEach(callbackfn: (value: T, value2: T, set: Set<T>) => void): void {\n\t\tthis.items.forEach((item) => {\n\t\t\tcallbackfn(item, item, this);\n\t\t});\n\t}\n\thas(value: T): boolean {\n\t\treturn this.items.indexOf(value) !== -1;\n\t}\n\n\tget size(): number {\n\t\treturn this.items.length;\n\t}\n}\n\nexport default typeof Set === \"undefined\" ? Set : SetLike;\n"], "mappings": ";;;;;;;AAmBA;AAAA,IACMA,OAAO;EAGZ,mBAA6B;IAAA,IAAjBC,KAAU,uEAAG,EAAE;IAAA;IAAA;IAC1B,IAAI,CAACA,KAAK,GAAGA,KAAK;EACnB;EAAC;IAAA;IAAA,OAED,aAAIC,KAAQ,EAAQ;MACnB,IAAI,IAAI,CAACC,GAAG,CAACD,KAAK,CAAC,KAAK,KAAK,EAAE;QAC9B,IAAI,CAACD,KAAK,CAACG,IAAI,CAACF,KAAK,CAAC;MACvB;MACA,OAAO,IAAI;IACZ;EAAC;IAAA;IAAA,OACD,iBAAc;MACb,IAAI,CAACD,KAAK,GAAG,EAAE;IAChB;EAAC;IAAA;IAAA,OACD,iBAAOC,KAAQ,EAAW;MACzB,IAAMG,cAAc,GAAG,IAAI,CAACJ,KAAK,CAACK,MAAM;MACxC,IAAI,CAACL,KAAK,GAAG,IAAI,CAACA,KAAK,CAACM,MAAM,CAAC,UAACC,IAAI;QAAA,OAAKA,IAAI,KAAKN,KAAK;MAAA,EAAC;MAExD,OAAOG,cAAc,KAAK,IAAI,CAACJ,KAAK,CAACK,MAAM;IAC5C;EAAC;IAAA;IAAA,OACD,iBAAQG,UAAsD,EAAQ;MAAA;MACrE,IAAI,CAACR,KAAK,CAACS,OAAO,CAAC,UAACF,IAAI,EAAK;QAC5BC,UAAU,CAACD,IAAI,EAAEA,IAAI,EAAE,KAAI,CAAC;MAC7B,CAAC,CAAC;IACH;EAAC;IAAA;IAAA,OACD,aAAIN,KAAQ,EAAW;MACtB,OAAO,IAAI,CAACD,KAAK,CAACU,OAAO,CAACT,KAAK,CAAC,KAAK,CAAC,CAAC;IACxC;EAAC;IAAA;IAAA,KAED,eAAmB;MAClB,OAAO,IAAI,CAACD,KAAK,CAACK,MAAM;IACzB;EAAC;EAAA;AAAA;AAGF,eAAe,OAAOM,GAAG,KAAK,WAAW,GAAGA,GAAG,GAAGZ,OAAO"}