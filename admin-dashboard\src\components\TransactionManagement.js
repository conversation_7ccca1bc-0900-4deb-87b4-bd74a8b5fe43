import React, { useState, useEffect } from 'react';
import Layout from './Layout';
import { FaSearch, FaEye, FaDownload, FaFilter, FaReceipt } from 'react-icons/fa';
import { transactionAPI } from '../services/api';
import { toast } from 'react-toastify';

const TransactionManagement = () => {
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedTransaction, setSelectedTransaction] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [filters, setFilters] = useState({
    status: '',
    dateFrom: '',
    dateTo: '',
    minAmount: '',
    maxAmount: ''
  });
  const [showFilters, setShowFilters] = useState(false);
  const [stats, setStats] = useState({
    totalTransactions: 0,
    totalAmount: 0,
    completedTransactions: 0,
    pendingTransactions: 0
  });

  useEffect(() => {
    fetchTransactions();
    fetchTransactionStats();
  }, [currentPage, searchTerm, filters]);

  const fetchTransactions = async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        limit: 10,
        search: searchTerm,
        ...filters
      };
      
      const response = await transactionAPI.getAllTransactions(params);
      setTransactions(response.data.transactions || []);
      setTotalPages(response.data.totalPages || 1);
    } catch (error) {
      console.error('Error fetching transactions:', error);
      toast.error('Failed to fetch transactions');
    } finally {
      setLoading(false);
    }
  };

  const fetchTransactionStats = async () => {
    try {
      const response = await transactionAPI.getTransactionStats();
      setStats(response.data.stats || {});
    } catch (error) {
      console.error('Error fetching transaction stats:', error);
    }
  };

  const handleViewTransaction = async (transactionId) => {
    try {
      const response = await transactionAPI.getTransactionById(transactionId);
      setSelectedTransaction(response.data.transaction);
      setShowModal(true);
    } catch (error) {
      console.error('Error fetching transaction details:', error);
      toast.error('Failed to fetch transaction details');
    }
  };

  const handleExportTransactions = async () => {
    try {
      const params = {
        search: searchTerm,
        ...filters
      };
      
      const response = await transactionAPI.exportTransactions(params);
      
      // Create blob and download
      const blob = new Blob([response.data], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `transactions_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      toast.success('Transactions exported successfully');
    } catch (error) {
      console.error('Error exporting transactions:', error);
      toast.error('Failed to export transactions');
    }
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
    setCurrentPage(1);
  };

  const clearFilters = () => {
    setFilters({
      status: '',
      dateFrom: '',
      dateTo: '',
      minAmount: '',
      maxAmount: ''
    });
    setCurrentPage(1);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedTransaction(null);
  };

  const getStatusBadgeClass = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return 'status-completed';
      case 'pending':
        return 'status-pending';
      case 'failed':
        return 'status-failed';
      default:
        return 'status-pending';
    }
  };

  const StatCard = ({ title, value, icon: Icon, color, format = 'number' }) => (
    <div className="stat-card">
      <div className="stat-content">
        <div className="stat-info">
          <h3>{title}</h3>
          <p>
            {format === 'currency'
              ? `$${(value || 0).toLocaleString(undefined, { minimumFractionDigits: 2 })}`
              : (value || 0).toLocaleString()
            }
          </p>
        </div>
        <div className="stat-icon" style={{ backgroundColor: color }}>
          <Icon />
        </div>
      </div>
    </div>
  );

  return (
    <Layout>
      <div className="transaction-management">
        <div className="page-header">
          <h1>Transaction Management</h1>
          <div className="header-actions">
            <button 
              className="btn btn-secondary"
              onClick={() => setShowFilters(!showFilters)}
            >
              <FaFilter /> Filters
            </button>
            <button 
              className="btn btn-success"
              onClick={handleExportTransactions}
            >
              <FaDownload /> Export
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="stats-grid">
          <StatCard
            title="Total Transactions"
            value={stats.totalTransactions}
            icon={FaReceipt}
            color="#007bff"
          />
          <StatCard
            title="Total Amount"
            value={stats.totalAmount}
            icon={FaReceipt}
            color="#28a745"
            format="currency"
          />
          <StatCard
            title="Completed"
            value={stats.completedTransactions}
            icon={FaReceipt}
            color="#17a2b8"
          />
          <StatCard
            title="Pending"
            value={stats.pendingTransactions}
            icon={FaReceipt}
            color="#ffc107"
          />
        </div>

        {/* Search Bar */}
        <div className="search-bar">
          <div className="search-input">
            <FaSearch className="search-icon" />
            <input
              type="text"
              placeholder="Search transactions by ID, user, or amount..."
              value={searchTerm}
              onChange={handleSearch}
              className="form-control"
            />
          </div>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="filters-panel">
            <div className="filters-grid">
              <div className="form-group">
                <label>Status</label>
                <select
                  name="status"
                  value={filters.status}
                  onChange={handleFilterChange}
                  className="form-control"
                >
                  <option value="">All Statuses</option>
                  <option value="completed">Completed</option>
                  <option value="pending">Pending</option>
                  <option value="failed">Failed</option>
                </select>
              </div>
              
              <div className="form-group">
                <label>Date From</label>
                <input
                  type="date"
                  name="dateFrom"
                  value={filters.dateFrom}
                  onChange={handleFilterChange}
                  className="form-control"
                />
              </div>
              
              <div className="form-group">
                <label>Date To</label>
                <input
                  type="date"
                  name="dateTo"
                  value={filters.dateTo}
                  onChange={handleFilterChange}
                  className="form-control"
                />
              </div>
              
              <div className="form-group">
                <label>Min Amount</label>
                <input
                  type="number"
                  step="0.01"
                  name="minAmount"
                  value={filters.minAmount}
                  onChange={handleFilterChange}
                  className="form-control"
                  placeholder="0.00"
                />
              </div>
              
              <div className="form-group">
                <label>Max Amount</label>
                <input
                  type="number"
                  step="0.01"
                  name="maxAmount"
                  value={filters.maxAmount}
                  onChange={handleFilterChange}
                  className="form-control"
                  placeholder="0.00"
                />
              </div>
              
              <div className="form-group">
                <label>&nbsp;</label>
                <button 
                  className="btn btn-secondary"
                  onClick={clearFilters}
                >
                  Clear Filters
                </button>
              </div>
            </div>
          </div>
        )}

        {loading ? (
          <div className="loading">Loading transactions...</div>
        ) : (
          <>
            <div className="card">
              <div className="table-responsive">
                <table className="table">
                  <thead>
                    <tr>
                      <th>Transaction ID</th>
                      <th>User</th>
                      <th>Amount</th>
                      <th>Status</th>
                      <th>Type</th>
                      <th>Date</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {transactions.map((transaction) => (
                      <tr key={transaction.id}>
                        <td>#{transaction.id}</td>
                        <td>{transaction.userName || 'N/A'}</td>
                        <td>${transaction.amount?.toFixed(2) || '0.00'}</td>
                        <td>
                          <span className={`status-badge ${getStatusBadgeClass(transaction.status)}`}>
                            {transaction.status || 'Pending'}
                          </span>
                        </td>
                        <td className="capitalize">{transaction.type || 'N/A'}</td>
                        <td>{transaction.createdAt ? new Date(transaction.createdAt).toLocaleDateString() : 'N/A'}</td>
                        <td>
                          <button
                            className="btn btn-primary btn-sm"
                            onClick={() => handleViewTransaction(transaction.id)}
                            title="View Details"
                          >
                            <FaEye />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                
                {transactions.length === 0 && (
                  <div className="empty-state">
                    <FaReceipt />
                    <p>No transactions found</p>
                  </div>
                )}
              </div>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="pagination">
                <button
                  className="btn btn-secondary"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </button>
                <span className="page-info">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  className="btn btn-secondary"
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </button>
              </div>
            )}
          </>
        )}

        {/* Transaction Details Modal */}
        {showModal && selectedTransaction && (
          <div className="modal-overlay" onClick={closeModal}>
            <div className="modal" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h2>Transaction Details</h2>
                <button className="modal-close" onClick={closeModal}>×</button>
              </div>
              
              <div className="modal-body">
                <div className="transaction-details">
                  <div className="detail-row">
                    <label>Transaction ID:</label>
                    <span>#{selectedTransaction.id}</span>
                  </div>
                  <div className="detail-row">
                    <label>User:</label>
                    <span>{selectedTransaction.userName || 'N/A'}</span>
                  </div>
                  <div className="detail-row">
                    <label>Amount:</label>
                    <span>${selectedTransaction.amount?.toFixed(2) || '0.00'}</span>
                  </div>
                  <div className="detail-row">
                    <label>Status:</label>
                    <span className={`status-badge ${getStatusBadgeClass(selectedTransaction.status)}`}>
                      {selectedTransaction.status || 'Pending'}
                    </span>
                  </div>
                  <div className="detail-row">
                    <label>Type:</label>
                    <span className="capitalize">{selectedTransaction.type || 'N/A'}</span>
                  </div>
                  <div className="detail-row">
                    <label>Description:</label>
                    <span>{selectedTransaction.description || 'N/A'}</span>
                  </div>
                  <div className="detail-row">
                    <label>Payment Method:</label>
                    <span className="capitalize">{selectedTransaction.paymentMethod || 'N/A'}</span>
                  </div>
                  <div className="detail-row">
                    <label>Reference:</label>
                    <span>{selectedTransaction.reference || 'N/A'}</span>
                  </div>
                  <div className="detail-row">
                    <label>Created Date:</label>
                    <span>{selectedTransaction.createdAt ? new Date(selectedTransaction.createdAt).toLocaleString() : 'N/A'}</span>
                  </div>
                  {selectedTransaction.updatedAt && (
                    <div className="detail-row">
                      <label>Updated Date:</label>
                      <span>{new Date(selectedTransaction.updatedAt).toLocaleString()}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      <style jsx>{`
        .transaction-management {
          max-width: 1200px;
          margin: 0 auto;
        }

        .page-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 30px;
        }

        .page-header h1 {
          margin: 0;
          color: #333;
        }

        .header-actions {
          display: flex;
          gap: 12px;
        }

        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 20px;
          margin-bottom: 30px;
        }

        .stat-card {
          background: white;
          border-radius: 12px;
          padding: 20px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .stat-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .stat-info h3 {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
          font-weight: 500;
        }

        .stat-info p {
          font-size: 24px;
          font-weight: 700;
          color: #333;
          margin: 0;
        }

        .stat-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 18px;
        }

        .search-bar {
          margin-bottom: 20px;
        }

        .search-input {
          position: relative;
          max-width: 500px;
        }

        .search-icon {
          position: absolute;
          left: 12px;
          top: 50%;
          transform: translateY(-50%);
          color: #666;
          font-size: 16px;
        }

        .search-input .form-control {
          padding-left: 40px;
        }

        .filters-panel {
          background: white;
          border-radius: 8px;
          padding: 20px;
          margin-bottom: 20px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .filters-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 20px;
          align-items: end;
        }

        .status-badge {
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
          text-transform: capitalize;
        }

        .status-completed {
          background-color: #d4edda;
          color: #155724;
        }

        .status-pending {
          background-color: #fff3cd;
          color: #856404;
        }

        .status-failed {
          background-color: #f8d7da;
          color: #721c24;
        }

        .capitalize {
          text-transform: capitalize;
        }

        .empty-state {
          text-align: center;
          padding: 40px;
          color: #666;
        }

        .empty-state svg {
          font-size: 48px;
          margin-bottom: 16px;
          opacity: 0.5;
        }

        .pagination {
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 20px;
          margin-top: 20px;
        }

        .page-info {
          font-size: 14px;
          color: #666;
        }

        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }

        .modal {
          background: white;
          border-radius: 12px;
          width: 90%;
          max-width: 600px;
          max-height: 90vh;
          overflow-y: auto;
        }

        .modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20px;
          border-bottom: 1px solid #dee2e6;
        }

        .modal-header h2 {
          margin: 0;
          color: #333;
        }

        .modal-close {
          background: none;
          border: none;
          font-size: 24px;
          color: #666;
          cursor: pointer;
          padding: 0;
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .modal-body {
          padding: 20px;
        }

        .transaction-details {
          display: grid;
          gap: 15px;
        }

        .detail-row {
          display: grid;
          grid-template-columns: 150px 1fr;
          gap: 15px;
          align-items: center;
          padding: 10px 0;
          border-bottom: 1px solid #f0f0f0;
        }

        .detail-row:last-child {
          border-bottom: none;
        }

        .detail-row label {
          font-weight: 600;
          color: #333;
        }

        .detail-row span {
          color: #666;
        }

        @media (max-width: 768px) {
          .page-header {
            flex-direction: column;
            gap: 20px;
            align-items: stretch;
          }

          .header-actions {
            justify-content: center;
          }

          .stats-grid {
            grid-template-columns: 1fr;
          }

          .filters-grid {
            grid-template-columns: 1fr;
          }

          .modal {
            width: 95%;
            margin: 20px;
          }

          .detail-row {
            grid-template-columns: 1fr;
            gap: 5px;
          }
        }
      `}</style>
    </Layout>
  );
};

export default TransactionManagement;
