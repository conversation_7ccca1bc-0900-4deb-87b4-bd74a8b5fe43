{"name": "deep-equal", "version": "2.2.3", "description": "node's assert.deepEqual algorithm", "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package": "./package.json", "./package.json": "./package.json"}, "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint --ext=js,mjs .", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "dependencies": {"array-buffer-byte-length": "^1.0.0", "call-bind": "^1.0.5", "es-get-iterator": "^1.1.3", "get-intrinsic": "^1.2.2", "is-arguments": "^1.1.1", "is-array-buffer": "^3.0.2", "is-date-object": "^1.0.5", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.2", "isarray": "^2.0.5", "object-is": "^1.1.5", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.5.1", "side-channel": "^1.0.4", "which-boxed-primitive": "^1.0.2", "which-collection": "^1.0.1", "which-typed-array": "^1.1.13"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "aud": "^2.0.3", "auto-changelog": "^2.4.0", "available-typed-arrays": "^1.0.5", "eslint": "=8.8.0", "for-each": "^0.3.3", "has-proto": "^1.0.1", "has-symbols": "^1.0.3", "has-typed-arrays": "^1.0.1", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "object.getownpropertydescriptors": "^2.1.7", "safe-publish-latest": "^2.0.0", "semver": "^6.3.1", "tape": "^5.7.2"}, "repository": {"type": "git", "url": "http://github.com/inspect-js/node-deep-equal.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "contributors": ["<PERSON> <<EMAIL>> (https://substack.net)", "<PERSON> <<EMAIL>>"], "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "engines": {"node": ">= 0.4"}, "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "browser": {"assert.js": false}, "publishConfig": {"ignore": [".github/workflows"]}}