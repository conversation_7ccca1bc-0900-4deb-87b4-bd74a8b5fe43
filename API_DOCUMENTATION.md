# Customer Authentication API Documentation

## Overview
This API provides authentication endpoints for customer registration and login, supporting both Arabic and English inputs.

## Base URL
```
http://localhost:3000/api
```

## Endpoints

### 1. Customer Registration
**POST** `/customers/signup`

Register a new customer account.

#### Request Body
```json
{
    "name": "أحمد محمد",
    "username": "ahmed123",
    "email": "<EMAIL>",
    "password": "password123",
    "phone": "+************",
    "Address": "123 شارع النيل، القاهرة، مصر",
    "DOB": "1990-05-15",
    "Gender": "ذكر",
    "communicationType": "كلاهما",
    "profilePicture": "profile_001.jpg"
}
```

#### Field Descriptions
- **name**: Customer's full name (2-50 characters)
- **username**: Unique username (3-30 characters, alphanumeric and underscore only)
- **email**: Valid email address
- **password**: Password (minimum 6 characters, must contain letters and numbers)
- **phone**: Valid phone number
- **Address**: Customer's address (5-200 characters)
- **DOB**: Date of birth (YYYY-MM-DD format)
- **Gender**: Gender (`male`, `female`, `ذكر`, `أنثى`)
- **communicationType**: Preferred communication method (`email`, `phone`, `both`, `بريد إلكتروني`, `هاتف`, `كلاهما`)
- **profilePicture**: Profile picture filename (optional)

#### Success Response (201)
```json
{
    "success": true,
    "message": "User registered successfully",
    "data": {
        "user": {
            "id": "user_id",
            "name": "أحمد محمد",
            "username": "ahmed123",
            "email": "<EMAIL>",
            "phone": "+************",
            "Address": "123 شارع النيل، القاهرة، مصر",
            "DOB": "1990-05-15T00:00:00.000Z",
            "Gender": "ذكر",
            "communicationType": "كلاهما"
        },
        "token": "jwt_token_here"
    }
}
```

### 2. Customer Login
**POST** `/customers/signin`

Authenticate a customer and receive a JWT token.

#### Request Body
```json
{
    "username": "ahmed123",
    "password": "password123"
}
```

#### Success Response (200)
```json
{
    "success": true,
    "message": "Login successful",
    "data": {
        "user": {
            "id": "user_id",
            "name": "أحمد محمد",
            "username": "ahmed123",
            "email": "<EMAIL>",
            "phone": "+************",
            "Address": "123 شارع النيل، القاهرة، مصر",
            "DOB": "1990-05-15T00:00:00.000Z",
            "Gender": "ذكر",
            "communicationType": "كلاهما"
        },
        "token": "jwt_token_here"
    }
}
```

### 3. Get Customer Profile
**GET** `/customers/profile`

Get the authenticated customer's profile information.

#### Headers
```
Authorization: Bearer <jwt_token>
```

#### Success Response (200)
```json
{
    "success": true,
    "message": "Profile retrieved successfully",
    "data": {
        "user": {
            "id": "user_id",
            "name": "أحمد محمد",
            "username": "ahmed123",
            "email": "<EMAIL>",
            "phone": "+************",
            "Address": "123 شارع النيل، القاهرة، مصر",
            "DOB": "1990-05-15T00:00:00.000Z",
            "Gender": "ذكر",
            "communicationType": "كلاهما",
            "createdAt": "2024-01-01T00:00:00.000Z",
            "updatedAt": "2024-01-01T00:00:00.000Z"
        }
    }
}
```

### 4. Health Check
**GET** `/health`

Check if the server is running.

#### Success Response (200)
```json
{
    "success": true,
    "message": "Server is running",
    "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Error Responses

### Validation Error (400)
```json
{
    "success": false,
    "message": "Validation failed",
    "errors": [
        {
            "msg": "Email is invalid",
            "param": "email",
            "location": "body"
        }
    ]
}
```

### User Already Exists (409)
```json
{
    "success": false,
    "message": "User with this username or email already exists"
}
```

### Invalid Credentials (401)
```json
{
    "success": false,
    "message": "Invalid username or password"
}
```

### Unauthorized (401)
```json
{
    "success": false,
    "message": "Access denied. No token provided."
}
```

### Internal Server Error (500)
```json
{
    "success": false,
    "message": "Internal server error",
    "error": "Error details"
}
```

## Authentication
The API uses JWT (JSON Web Tokens) for authentication. After successful login or registration, include the token in the Authorization header:

```
Authorization: Bearer <your_jwt_token>
```

Tokens expire after 1 hour.

## Testing
Use the provided HTTP files in the `HTTPS` folder to test the API endpoints:
- `signup.http` - Test customer registration
- `login.http` - Test customer login
- `customer-profile.http` - Test protected routes
