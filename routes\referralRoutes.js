const express = require('express');
const router = express.Router();
const referralController = require('../controller/referralController');
const { auth } = require('../middelwares/authorization');

// Get customer's referral information
router.get('/info', auth, referralController.getReferralInfo);

// Get customer's referral network (direct referrals)
router.get('/network', auth, referralController.getReferralNetwork);

// Validate referral code (public endpoint for registration)
router.post('/validate', referralController.validateReferralCode);

// Get referral statistics
router.get('/stats', auth, referralController.getReferralStats);

module.exports = router;
