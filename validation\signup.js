const {check}=require('express-validator');

module.exports.validatePostUser=()=>{
    const validationMiddleware=[
        check('name')
            .notEmpty()
            .withMessage('Name cannot be empty')
            .isLength({ min: 2, max: 50 })
            .withMessage('Name must be between 2 and 50 characters'),

        check('username')
            .notEmpty()
            .withMessage('Username cannot be empty')
            .isLength({ min: 3, max: 30 })
            .withMessage('Username must be between 3 and 30 characters')
            .matches(/^[a-zA-Z0-9_]+$/)
            .withMessage('Username can only contain letters, numbers, and underscores'),

        check('email')
            .isEmail()
            .withMessage('Email is invalid')
            .normalizeEmail(),

        check('password')
            .isLength({ min: 6 })
            .withMessage('Password must be at least 6 characters long')
            .matches(/^(?=.*[a-zA-Z])(?=.*\d)/)
            .withMessage('Password must contain at least one letter and one number'),

        check('phone')
            .notEmpty()
            .withMessage('Phone number cannot be empty')
            .isMobilePhone()
            .withMessage('Please provide a valid phone number'),

        check('Address')
            .notEmpty()
            .withMessage('Address cannot be empty')
            .isLength({ min: 5, max: 200 })
            .withMessage('Address must be between 5 and 200 characters'),

        check('DOB')
            .notEmpty()
            .withMessage('Date of birth cannot be empty')
            .isISO8601()
            .withMessage('Please provide a valid date format (YYYY-MM-DD)'),

        check('Gender')
            .notEmpty()
            .withMessage('Gender cannot be empty')
            .isIn(['male', 'female', 'ذكر', 'أنثى'])
            .withMessage('Gender must be either male, female, ذكر, or أنثى'),

        check('communicationType')
            .notEmpty()
            .withMessage('Communication type cannot be empty')
            .isIn(['email', 'phone', 'both', 'بريد إلكتروني', 'هاتف', 'كلاهما'])
            .withMessage('Invalid communication type')

        // Note: profilePicture is handled as file upload, not in body validation
    ]
    return validationMiddleware;
}

module.exports.validateLogin=()=>{
    const validationMiddleware=[
        check('username')
            .notEmpty()
            .withMessage('Username cannot be empty'),

        check('password')
            .notEmpty()
            .withMessage('Password cannot be empty')
    ]
    return validationMiddleware;
}