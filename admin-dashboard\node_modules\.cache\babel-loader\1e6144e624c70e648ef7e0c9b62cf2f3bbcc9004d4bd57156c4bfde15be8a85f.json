{"ast": null, "code": "var _jsxFileName = \"D:\\\\Node-project\\\\admin-dashboard\\\\src\\\\components\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Layout from './Layout';\nimport { FaUsers, FaBox, FaReceipt, FaUserShield, FaArrowUp, FaArrowDown, FaEye } from 'react-icons/fa';\nimport { userAPI, productAPI, transactionAPI, adminAPI } from '../services/api';\nimport { toast } from 'react-toastify';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const [stats, setStats] = useState({\n    users: {\n      total: 0,\n      change: 0\n    },\n    products: {\n      total: 0,\n      change: 0\n    },\n    transactions: {\n      total: 0,\n      change: 0\n    },\n    admins: {\n      total: 0,\n      change: 0\n    }\n  });\n  const [recentTransactions, setRecentTransactions] = useState([]);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n\n      // Fetch all stats in parallel\n      const [usersRes, productsRes, transactionsRes, adminsRes] = await Promise.allSettled([userAPI.getUserStats(), productAPI.getAllProducts({\n        limit: 1\n      }), transactionAPI.getTransactionStats(), adminAPI.getAllAdmins()]);\n\n      // Process results\n      const newStats = {\n        ...stats\n      };\n      console.log('API Responses:', {\n        users: usersRes.status === 'fulfilled' ? usersRes.value.data : usersRes.reason,\n        products: productsRes.status === 'fulfilled' ? productsRes.value.data : productsRes.reason,\n        transactions: transactionsRes.status === 'fulfilled' ? transactionsRes.value.data : transactionsRes.reason,\n        admins: adminsRes.status === 'fulfilled' ? adminsRes.value.data : adminsRes.reason\n      });\n      if (usersRes.status === 'fulfilled') {\n        newStats.users = {\n          total: usersRes.value.data.totalUsers || 0,\n          change: usersRes.value.data.monthlyGrowth || 0\n        };\n        console.log('Users stats:', newStats.users);\n      }\n      if (productsRes.status === 'fulfilled') {\n        newStats.products = {\n          total: productsRes.value.data.totalProducts || 0,\n          change: 0 // You can implement product growth tracking\n        };\n        console.log('Products stats:', newStats.products);\n      }\n      if (transactionsRes.status === 'fulfilled') {\n        var _transactionsRes$valu;\n        newStats.transactions = {\n          total: ((_transactionsRes$valu = transactionsRes.value.data.stats) === null || _transactionsRes$valu === void 0 ? void 0 : _transactionsRes$valu.totalTransactions) || 0,\n          change: transactionsRes.value.data.monthlyGrowth || 0\n        };\n        console.log('Transactions stats:', newStats.transactions);\n      }\n      if (adminsRes.status === 'fulfilled') {\n        var _adminsRes$value$data;\n        newStats.admins = {\n          total: ((_adminsRes$value$data = adminsRes.value.data.admins) === null || _adminsRes$value$data === void 0 ? void 0 : _adminsRes$value$data.length) || 0,\n          change: 0\n        };\n        console.log('Admins stats:', newStats.admins);\n      }\n      setStats(newStats);\n\n      // Fetch recent transactions\n      try {\n        const recentRes = await transactionAPI.getAllTransactions({\n          limit: 5,\n          sort: 'createdAt',\n          order: 'desc'\n        });\n        setRecentTransactions(recentRes.data.transactions || []);\n      } catch (error) {\n        console.error('Error fetching recent transactions:', error);\n      }\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n      toast.error('Failed to load dashboard data');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const StatCard = ({\n    title,\n    value,\n    change,\n    icon: Icon,\n    color,\n    link\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"stat-card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stat-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"stat-title\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"stat-value\",\n          children: loading ? '...' : (value || 0).toLocaleString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-icon\",\n        style: {\n          backgroundColor: color\n        },\n        children: /*#__PURE__*/_jsxDEV(Icon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stat-footer\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `stat-change ${(change || 0) >= 0 ? 'positive' : 'negative'}`,\n        children: [(change || 0) >= 0 ? /*#__PURE__*/_jsxDEV(FaArrowUp, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 33\n        }, this) : /*#__PURE__*/_jsxDEV(FaArrowDown, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 49\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [Math.abs(change || 0), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"stat-period\",\n        children: \"vs last month\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 106,\n    columnNumber: 5\n  }, this);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: \"Loading dashboard...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stats-grid\",\n        children: [/*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Users\",\n          value: stats.users.total,\n          change: stats.users.change,\n          icon: FaUsers,\n          color: \"#007bff\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Products\",\n          value: stats.products.total,\n          change: stats.products.change,\n          icon: FaBox,\n          color: \"#ffc107\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Transactions\",\n          value: stats.transactions.total,\n          change: stats.transactions.change,\n          icon: FaReceipt,\n          color: \"#6f42c1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Admins\",\n          value: stats.admins.total,\n          change: stats.admins.change,\n          icon: FaUserShield,\n          color: \"#28a745\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"card-title\",\n              children: \"Recent Transactions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/transactions\",\n              className: \"btn btn-primary btn-sm\",\n              children: [/*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this), \" View All\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), recentTransactions.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-responsive\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"table\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Transaction ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"User\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 185,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Amount\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: recentTransactions.map(transaction => {\n                  var _transaction$amount, _transaction$status;\n                  return /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      children: [\"#\", transaction.id]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 194,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: transaction.userName || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 195,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: [\"$\", ((_transaction$amount = transaction.amount) === null || _transaction$amount === void 0 ? void 0 : _transaction$amount.toFixed(2)) || '0.00']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 196,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `status-badge ${(_transaction$status = transaction.status) === null || _transaction$status === void 0 ? void 0 : _transaction$status.toLowerCase()}`,\n                        children: transaction.status || 'Pending'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 198,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 197,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: new Date(transaction.createdAt).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 202,\n                      columnNumber: 25\n                    }, this)]\n                  }, transaction.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"empty-state\",\n            children: [/*#__PURE__*/_jsxDEV(FaReceipt, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"No recent transactions found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .dashboard {\n          max-width: 1200px;\n          margin: 0 auto;\n        }\n\n        .stats-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n          gap: 20px;\n          margin-bottom: 30px;\n        }\n\n        .stat-card {\n          background: white;\n          border-radius: 12px;\n          padding: 24px;\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n          transition: transform 0.3s ease, box-shadow 0.3s ease;\n        }\n\n        .stat-card:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n        }\n\n        .stat-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 16px;\n        }\n\n        .stat-title {\n          font-size: 14px;\n          color: #666;\n          margin-bottom: 8px;\n          font-weight: 500;\n        }\n\n        .stat-value {\n          font-size: 32px;\n          font-weight: 700;\n          color: #333;\n          margin: 0;\n        }\n\n        .stat-icon {\n          width: 48px;\n          height: 48px;\n          border-radius: 12px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          color: white;\n          font-size: 20px;\n        }\n\n        .stat-footer {\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n        }\n\n        .stat-change {\n          display: flex;\n          align-items: center;\n          gap: 4px;\n          font-size: 14px;\n          font-weight: 600;\n        }\n\n        .stat-change.positive {\n          color: #28a745;\n        }\n\n        .stat-change.negative {\n          color: #dc3545;\n        }\n\n        .stat-period {\n          font-size: 12px;\n          color: #666;\n        }\n\n        .dashboard-section {\n          margin-bottom: 30px;\n        }\n\n        .table-responsive {\n          overflow-x: auto;\n        }\n\n        .status-badge {\n          padding: 4px 8px;\n          border-radius: 12px;\n          font-size: 12px;\n          font-weight: 500;\n          text-transform: capitalize;\n        }\n\n        .status-badge.completed {\n          background-color: #d4edda;\n          color: #155724;\n        }\n\n        .status-badge.pending {\n          background-color: #fff3cd;\n          color: #856404;\n        }\n\n        .status-badge.failed {\n          background-color: #f8d7da;\n          color: #721c24;\n        }\n\n        .empty-state {\n          text-align: center;\n          padding: 40px;\n          color: #666;\n        }\n\n        .empty-state svg {\n          font-size: 48px;\n          margin-bottom: 16px;\n          opacity: 0.5;\n        }\n\n        @media (max-width: 768px) {\n          .stats-grid {\n            grid-template-columns: 1fr;\n          }\n          \n          .stat-value {\n            font-size: 24px;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 135,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"AgGVFpgsM+hSp4yXkMAx4vKLn+U=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Layout", "FaUsers", "FaBox", "FaReceipt", "FaUserShield", "FaArrowUp", "FaArrowDown", "FaEye", "userAPI", "productAPI", "transactionAPI", "adminAPI", "toast", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "stats", "setStats", "users", "total", "change", "products", "transactions", "admins", "recentTransactions", "setRecentTransactions", "loading", "setLoading", "fetchDashboardData", "usersRes", "productsRes", "transactionsRes", "adminsRes", "Promise", "allSettled", "getUserStats", "getAllProducts", "limit", "getTransactionStats", "getAllAdmins", "newStats", "console", "log", "status", "value", "data", "reason", "totalUsers", "monthlyGrowth", "totalProducts", "_transactionsRes$valu", "totalTransactions", "_adminsRes$value$data", "length", "recentRes", "getAllTransactions", "sort", "order", "error", "StatCard", "title", "icon", "Icon", "color", "link", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toLocaleString", "style", "backgroundColor", "Math", "abs", "href", "map", "transaction", "_transaction$amount", "_transaction$status", "id", "userName", "amount", "toFixed", "toLowerCase", "Date", "createdAt", "toLocaleDateString", "jsx", "_c", "$RefreshReg$"], "sources": ["D:/Node-project/admin-dashboard/src/components/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Layout from './Layout';\nimport { \n  FaUsers, \n  FaBox, \n  FaReceipt, \n  FaUserShield,\n  FaArrowUp,\n  FaArrowDown,\n  FaEye\n} from 'react-icons/fa';\nimport { userAPI, productAPI, transactionAPI, adminAPI } from '../services/api';\nimport { toast } from 'react-toastify';\n\nconst Dashboard = () => {\n  const [stats, setStats] = useState({\n    users: { total: 0, change: 0 },\n    products: { total: 0, change: 0 },\n    transactions: { total: 0, change: 0 },\n    admins: { total: 0, change: 0 }\n  });\n  const [recentTransactions, setRecentTransactions] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n      \n      // Fetch all stats in parallel\n      const [usersRes, productsRes, transactionsRes, adminsRes] = await Promise.allSettled([\n        userAPI.getUserStats(),\n        productAPI.getAllProducts({ limit: 1 }),\n        transactionAPI.getTransactionStats(),\n        adminAPI.getAllAdmins()\n      ]);\n\n      // Process results\n      const newStats = { ...stats };\n\n      console.log('API Responses:', {\n        users: usersRes.status === 'fulfilled' ? usersRes.value.data : usersRes.reason,\n        products: productsRes.status === 'fulfilled' ? productsRes.value.data : productsRes.reason,\n        transactions: transactionsRes.status === 'fulfilled' ? transactionsRes.value.data : transactionsRes.reason,\n        admins: adminsRes.status === 'fulfilled' ? adminsRes.value.data : adminsRes.reason\n      });\n\n      if (usersRes.status === 'fulfilled') {\n        newStats.users = {\n          total: usersRes.value.data.totalUsers || 0,\n          change: usersRes.value.data.monthlyGrowth || 0\n        };\n        console.log('Users stats:', newStats.users);\n      }\n\n      if (productsRes.status === 'fulfilled') {\n        newStats.products = {\n          total: productsRes.value.data.totalProducts || 0,\n          change: 0 // You can implement product growth tracking\n        };\n        console.log('Products stats:', newStats.products);\n      }\n\n      if (transactionsRes.status === 'fulfilled') {\n        newStats.transactions = {\n          total: transactionsRes.value.data.stats?.totalTransactions || 0,\n          change: transactionsRes.value.data.monthlyGrowth || 0\n        };\n        console.log('Transactions stats:', newStats.transactions);\n      }\n\n      if (adminsRes.status === 'fulfilled') {\n        newStats.admins = {\n          total: adminsRes.value.data.admins?.length || 0,\n          change: 0\n        };\n        console.log('Admins stats:', newStats.admins);\n      }\n\n      setStats(newStats);\n\n      // Fetch recent transactions\n      try {\n        const recentRes = await transactionAPI.getAllTransactions({ \n          limit: 5, \n          sort: 'createdAt',\n          order: 'desc' \n        });\n        setRecentTransactions(recentRes.data.transactions || []);\n      } catch (error) {\n        console.error('Error fetching recent transactions:', error);\n      }\n\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n      toast.error('Failed to load dashboard data');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const StatCard = ({ title, value, change, icon: Icon, color, link }) => (\n    <div className=\"stat-card\">\n      <div className=\"stat-header\">\n        <div className=\"stat-info\">\n          <h3 className=\"stat-title\">{title}</h3>\n          <p className=\"stat-value\">{loading ? '...' : (value || 0).toLocaleString()}</p>\n        </div>\n        <div className=\"stat-icon\" style={{ backgroundColor: color }}>\n          <Icon />\n        </div>\n      </div>\n      <div className=\"stat-footer\">\n        <div className={`stat-change ${(change || 0) >= 0 ? 'positive' : 'negative'}`}>\n          {(change || 0) >= 0 ? <FaArrowUp /> : <FaArrowDown />}\n          <span>{Math.abs(change || 0)}%</span>\n        </div>\n        <span className=\"stat-period\">vs last month</span>\n      </div>\n    </div>\n  );\n\n  if (loading) {\n    return (\n      <Layout>\n        <div className=\"loading\">Loading dashboard...</div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout>\n      <div className=\"dashboard\">\n        {/* Stats Grid */}\n        <div className=\"stats-grid\">\n          <StatCard\n            title=\"Total Users\"\n            value={stats.users.total}\n            change={stats.users.change}\n            icon={FaUsers}\n            color=\"#007bff\"\n          />\n          <StatCard\n            title=\"Total Products\"\n            value={stats.products.total}\n            change={stats.products.change}\n            icon={FaBox}\n            color=\"#ffc107\"\n          />\n          <StatCard\n            title=\"Total Transactions\"\n            value={stats.transactions.total}\n            change={stats.transactions.change}\n            icon={FaReceipt}\n            color=\"#6f42c1\"\n          />\n          <StatCard\n            title=\"Total Admins\"\n            value={stats.admins.total}\n            change={stats.admins.change}\n            icon={FaUserShield}\n            color=\"#28a745\"\n          />\n        </div>\n\n        {/* Recent Transactions */}\n        <div className=\"dashboard-section\">\n          <div className=\"card\">\n            <div className=\"card-header\">\n              <h2 className=\"card-title\">Recent Transactions</h2>\n              <a href=\"/transactions\" className=\"btn btn-primary btn-sm\">\n                <FaEye /> View All\n              </a>\n            </div>\n            \n            {recentTransactions.length > 0 ? (\n              <div className=\"table-responsive\">\n                <table className=\"table\">\n                  <thead>\n                    <tr>\n                      <th>Transaction ID</th>\n                      <th>User</th>\n                      <th>Amount</th>\n                      <th>Status</th>\n                      <th>Date</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {recentTransactions.map((transaction) => (\n                      <tr key={transaction.id}>\n                        <td>#{transaction.id}</td>\n                        <td>{transaction.userName || 'N/A'}</td>\n                        <td>${transaction.amount?.toFixed(2) || '0.00'}</td>\n                        <td>\n                          <span className={`status-badge ${transaction.status?.toLowerCase()}`}>\n                            {transaction.status || 'Pending'}\n                          </span>\n                        </td>\n                        <td>{new Date(transaction.createdAt).toLocaleDateString()}</td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            ) : (\n              <div className=\"empty-state\">\n                <FaReceipt />\n                <p>No recent transactions found</p>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      <style jsx>{`\n        .dashboard {\n          max-width: 1200px;\n          margin: 0 auto;\n        }\n\n        .stats-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n          gap: 20px;\n          margin-bottom: 30px;\n        }\n\n        .stat-card {\n          background: white;\n          border-radius: 12px;\n          padding: 24px;\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n          transition: transform 0.3s ease, box-shadow 0.3s ease;\n        }\n\n        .stat-card:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n        }\n\n        .stat-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 16px;\n        }\n\n        .stat-title {\n          font-size: 14px;\n          color: #666;\n          margin-bottom: 8px;\n          font-weight: 500;\n        }\n\n        .stat-value {\n          font-size: 32px;\n          font-weight: 700;\n          color: #333;\n          margin: 0;\n        }\n\n        .stat-icon {\n          width: 48px;\n          height: 48px;\n          border-radius: 12px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          color: white;\n          font-size: 20px;\n        }\n\n        .stat-footer {\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n        }\n\n        .stat-change {\n          display: flex;\n          align-items: center;\n          gap: 4px;\n          font-size: 14px;\n          font-weight: 600;\n        }\n\n        .stat-change.positive {\n          color: #28a745;\n        }\n\n        .stat-change.negative {\n          color: #dc3545;\n        }\n\n        .stat-period {\n          font-size: 12px;\n          color: #666;\n        }\n\n        .dashboard-section {\n          margin-bottom: 30px;\n        }\n\n        .table-responsive {\n          overflow-x: auto;\n        }\n\n        .status-badge {\n          padding: 4px 8px;\n          border-radius: 12px;\n          font-size: 12px;\n          font-weight: 500;\n          text-transform: capitalize;\n        }\n\n        .status-badge.completed {\n          background-color: #d4edda;\n          color: #155724;\n        }\n\n        .status-badge.pending {\n          background-color: #fff3cd;\n          color: #856404;\n        }\n\n        .status-badge.failed {\n          background-color: #f8d7da;\n          color: #721c24;\n        }\n\n        .empty-state {\n          text-align: center;\n          padding: 40px;\n          color: #666;\n        }\n\n        .empty-state svg {\n          font-size: 48px;\n          margin-bottom: 16px;\n          opacity: 0.5;\n        }\n\n        @media (max-width: 768px) {\n          .stats-grid {\n            grid-template-columns: 1fr;\n          }\n          \n          .stat-value {\n            font-size: 24px;\n          }\n        }\n      `}</style>\n    </Layout>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,UAAU;AAC7B,SACEC,OAAO,EACPC,KAAK,EACLC,SAAS,EACTC,YAAY,EACZC,SAAS,EACTC,WAAW,EACXC,KAAK,QACA,gBAAgB;AACvB,SAASC,OAAO,EAAEC,UAAU,EAAEC,cAAc,EAAEC,QAAQ,QAAQ,iBAAiB;AAC/E,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC;IACjCqB,KAAK,EAAE;MAAEC,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;IAC9BC,QAAQ,EAAE;MAAEF,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;IACjCE,YAAY,EAAE;MAAEH,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;IACrCG,MAAM,EAAE;MAAEJ,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE;EAChC,CAAC,CAAC;EACF,MAAM,CAACI,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd8B,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFD,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM,CAACE,QAAQ,EAAEC,WAAW,EAAEC,eAAe,EAAEC,SAAS,CAAC,GAAG,MAAMC,OAAO,CAACC,UAAU,CAAC,CACnF3B,OAAO,CAAC4B,YAAY,CAAC,CAAC,EACtB3B,UAAU,CAAC4B,cAAc,CAAC;QAAEC,KAAK,EAAE;MAAE,CAAC,CAAC,EACvC5B,cAAc,CAAC6B,mBAAmB,CAAC,CAAC,EACpC5B,QAAQ,CAAC6B,YAAY,CAAC,CAAC,CACxB,CAAC;;MAEF;MACA,MAAMC,QAAQ,GAAG;QAAE,GAAGxB;MAAM,CAAC;MAE7ByB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;QAC5BxB,KAAK,EAAEW,QAAQ,CAACc,MAAM,KAAK,WAAW,GAAGd,QAAQ,CAACe,KAAK,CAACC,IAAI,GAAGhB,QAAQ,CAACiB,MAAM;QAC9EzB,QAAQ,EAAES,WAAW,CAACa,MAAM,KAAK,WAAW,GAAGb,WAAW,CAACc,KAAK,CAACC,IAAI,GAAGf,WAAW,CAACgB,MAAM;QAC1FxB,YAAY,EAAES,eAAe,CAACY,MAAM,KAAK,WAAW,GAAGZ,eAAe,CAACa,KAAK,CAACC,IAAI,GAAGd,eAAe,CAACe,MAAM;QAC1GvB,MAAM,EAAES,SAAS,CAACW,MAAM,KAAK,WAAW,GAAGX,SAAS,CAACY,KAAK,CAACC,IAAI,GAAGb,SAAS,CAACc;MAC9E,CAAC,CAAC;MAEF,IAAIjB,QAAQ,CAACc,MAAM,KAAK,WAAW,EAAE;QACnCH,QAAQ,CAACtB,KAAK,GAAG;UACfC,KAAK,EAAEU,QAAQ,CAACe,KAAK,CAACC,IAAI,CAACE,UAAU,IAAI,CAAC;UAC1C3B,MAAM,EAAES,QAAQ,CAACe,KAAK,CAACC,IAAI,CAACG,aAAa,IAAI;QAC/C,CAAC;QACDP,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,QAAQ,CAACtB,KAAK,CAAC;MAC7C;MAEA,IAAIY,WAAW,CAACa,MAAM,KAAK,WAAW,EAAE;QACtCH,QAAQ,CAACnB,QAAQ,GAAG;UAClBF,KAAK,EAAEW,WAAW,CAACc,KAAK,CAACC,IAAI,CAACI,aAAa,IAAI,CAAC;UAChD7B,MAAM,EAAE,CAAC,CAAC;QACZ,CAAC;QACDqB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,QAAQ,CAACnB,QAAQ,CAAC;MACnD;MAEA,IAAIU,eAAe,CAACY,MAAM,KAAK,WAAW,EAAE;QAAA,IAAAO,qBAAA;QAC1CV,QAAQ,CAAClB,YAAY,GAAG;UACtBH,KAAK,EAAE,EAAA+B,qBAAA,GAAAnB,eAAe,CAACa,KAAK,CAACC,IAAI,CAAC7B,KAAK,cAAAkC,qBAAA,uBAAhCA,qBAAA,CAAkCC,iBAAiB,KAAI,CAAC;UAC/D/B,MAAM,EAAEW,eAAe,CAACa,KAAK,CAACC,IAAI,CAACG,aAAa,IAAI;QACtD,CAAC;QACDP,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEF,QAAQ,CAAClB,YAAY,CAAC;MAC3D;MAEA,IAAIU,SAAS,CAACW,MAAM,KAAK,WAAW,EAAE;QAAA,IAAAS,qBAAA;QACpCZ,QAAQ,CAACjB,MAAM,GAAG;UAChBJ,KAAK,EAAE,EAAAiC,qBAAA,GAAApB,SAAS,CAACY,KAAK,CAACC,IAAI,CAACtB,MAAM,cAAA6B,qBAAA,uBAA3BA,qBAAA,CAA6BC,MAAM,KAAI,CAAC;UAC/CjC,MAAM,EAAE;QACV,CAAC;QACDqB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEF,QAAQ,CAACjB,MAAM,CAAC;MAC/C;MAEAN,QAAQ,CAACuB,QAAQ,CAAC;;MAElB;MACA,IAAI;QACF,MAAMc,SAAS,GAAG,MAAM7C,cAAc,CAAC8C,kBAAkB,CAAC;UACxDlB,KAAK,EAAE,CAAC;UACRmB,IAAI,EAAE,WAAW;UACjBC,KAAK,EAAE;QACT,CAAC,CAAC;QACFhC,qBAAqB,CAAC6B,SAAS,CAACT,IAAI,CAACvB,YAAY,IAAI,EAAE,CAAC;MAC1D,CAAC,CAAC,OAAOoC,KAAK,EAAE;QACdjB,OAAO,CAACiB,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC7D;IAEF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD/C,KAAK,CAAC+C,KAAK,CAAC,+BAA+B,CAAC;IAC9C,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgC,QAAQ,GAAGA,CAAC;IAAEC,KAAK;IAAEhB,KAAK;IAAExB,MAAM;IAAEyC,IAAI,EAAEC,IAAI;IAAEC,KAAK;IAAEC;EAAK,CAAC,kBACjEnD,OAAA;IAAKoD,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBrD,OAAA;MAAKoD,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BrD,OAAA;QAAKoD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBrD,OAAA;UAAIoD,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAEN;QAAK;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvCzD,OAAA;UAAGoD,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAExC,OAAO,GAAG,KAAK,GAAG,CAACkB,KAAK,IAAI,CAAC,EAAE2B,cAAc,CAAC;QAAC;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC,eACNzD,OAAA;QAAKoD,SAAS,EAAC,WAAW;QAACO,KAAK,EAAE;UAAEC,eAAe,EAAEV;QAAM,CAAE;QAAAG,QAAA,eAC3DrD,OAAA,CAACiD,IAAI;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNzD,OAAA;MAAKoD,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BrD,OAAA;QAAKoD,SAAS,EAAE,eAAe,CAAC7C,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,UAAU,GAAG,UAAU,EAAG;QAAA8C,QAAA,GAC3E,CAAC9C,MAAM,IAAI,CAAC,KAAK,CAAC,gBAAGP,OAAA,CAACT,SAAS;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGzD,OAAA,CAACR,WAAW;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDzD,OAAA;UAAAqD,QAAA,GAAOQ,IAAI,CAACC,GAAG,CAACvD,MAAM,IAAI,CAAC,CAAC,EAAC,GAAC;QAAA;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eACNzD,OAAA;QAAMoD,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,IAAI5C,OAAO,EAAE;IACX,oBACEb,OAAA,CAACd,MAAM;MAAAmE,QAAA,eACLrD,OAAA;QAAKoD,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC;EAEb;EAEA,oBACEzD,OAAA,CAACd,MAAM;IAAAmE,QAAA,gBACLrD,OAAA;MAAKoD,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAExBrD,OAAA;QAAKoD,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBrD,OAAA,CAAC8C,QAAQ;UACPC,KAAK,EAAC,aAAa;UACnBhB,KAAK,EAAE5B,KAAK,CAACE,KAAK,CAACC,KAAM;UACzBC,MAAM,EAAEJ,KAAK,CAACE,KAAK,CAACE,MAAO;UAC3ByC,IAAI,EAAE7D,OAAQ;UACd+D,KAAK,EAAC;QAAS;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACFzD,OAAA,CAAC8C,QAAQ;UACPC,KAAK,EAAC,gBAAgB;UACtBhB,KAAK,EAAE5B,KAAK,CAACK,QAAQ,CAACF,KAAM;UAC5BC,MAAM,EAAEJ,KAAK,CAACK,QAAQ,CAACD,MAAO;UAC9ByC,IAAI,EAAE5D,KAAM;UACZ8D,KAAK,EAAC;QAAS;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACFzD,OAAA,CAAC8C,QAAQ;UACPC,KAAK,EAAC,oBAAoB;UAC1BhB,KAAK,EAAE5B,KAAK,CAACM,YAAY,CAACH,KAAM;UAChCC,MAAM,EAAEJ,KAAK,CAACM,YAAY,CAACF,MAAO;UAClCyC,IAAI,EAAE3D,SAAU;UAChB6D,KAAK,EAAC;QAAS;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACFzD,OAAA,CAAC8C,QAAQ;UACPC,KAAK,EAAC,cAAc;UACpBhB,KAAK,EAAE5B,KAAK,CAACO,MAAM,CAACJ,KAAM;UAC1BC,MAAM,EAAEJ,KAAK,CAACO,MAAM,CAACH,MAAO;UAC5ByC,IAAI,EAAE1D,YAAa;UACnB4D,KAAK,EAAC;QAAS;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNzD,OAAA;QAAKoD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCrD,OAAA;UAAKoD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBrD,OAAA;YAAKoD,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BrD,OAAA;cAAIoD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnDzD,OAAA;cAAG+D,IAAI,EAAC,eAAe;cAACX,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACxDrD,OAAA,CAACP,KAAK;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,aACX;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,EAEL9C,kBAAkB,CAAC6B,MAAM,GAAG,CAAC,gBAC5BxC,OAAA;YAAKoD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/BrD,OAAA;cAAOoD,SAAS,EAAC,OAAO;cAAAC,QAAA,gBACtBrD,OAAA;gBAAAqD,QAAA,eACErD,OAAA;kBAAAqD,QAAA,gBACErD,OAAA;oBAAAqD,QAAA,EAAI;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvBzD,OAAA;oBAAAqD,QAAA,EAAI;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACbzD,OAAA;oBAAAqD,QAAA,EAAI;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACfzD,OAAA;oBAAAqD,QAAA,EAAI;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACfzD,OAAA;oBAAAqD,QAAA,EAAI;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRzD,OAAA;gBAAAqD,QAAA,EACG1C,kBAAkB,CAACqD,GAAG,CAAEC,WAAW;kBAAA,IAAAC,mBAAA,EAAAC,mBAAA;kBAAA,oBAClCnE,OAAA;oBAAAqD,QAAA,gBACErD,OAAA;sBAAAqD,QAAA,GAAI,GAAC,EAACY,WAAW,CAACG,EAAE;oBAAA;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC1BzD,OAAA;sBAAAqD,QAAA,EAAKY,WAAW,CAACI,QAAQ,IAAI;oBAAK;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACxCzD,OAAA;sBAAAqD,QAAA,GAAI,GAAC,EAAC,EAAAa,mBAAA,GAAAD,WAAW,CAACK,MAAM,cAAAJ,mBAAA,uBAAlBA,mBAAA,CAAoBK,OAAO,CAAC,CAAC,CAAC,KAAI,MAAM;oBAAA;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACpDzD,OAAA;sBAAAqD,QAAA,eACErD,OAAA;wBAAMoD,SAAS,EAAE,iBAAAe,mBAAA,GAAgBF,WAAW,CAACnC,MAAM,cAAAqC,mBAAA,uBAAlBA,mBAAA,CAAoBK,WAAW,CAAC,CAAC,EAAG;wBAAAnB,QAAA,EAClEY,WAAW,CAACnC,MAAM,IAAI;sBAAS;wBAAAwB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACLzD,OAAA;sBAAAqD,QAAA,EAAK,IAAIoB,IAAI,CAACR,WAAW,CAACS,SAAS,CAAC,CAACC,kBAAkB,CAAC;oBAAC;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA,GATxDQ,WAAW,CAACG,EAAE;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAUnB,CAAC;gBAAA,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,gBAENzD,OAAA;YAAKoD,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BrD,OAAA,CAACX,SAAS;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACbzD,OAAA;cAAAqD,QAAA,EAAG;YAA4B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENzD,OAAA;MAAO4E,GAAG;MAAAvB,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEb,CAAC;AAACvD,EAAA,CAvVID,SAAS;AAAA4E,EAAA,GAAT5E,SAAS;AAyVf,eAAeA,SAAS;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}