
POST http://localhost:3000/api/customers/signup
Content-Type: application/json

{
    "name": "أحمد محمد",
    "username": "ahmed123",
    "email": "<EMAIL>",
    "password": "password123",
    "phone": "+201234567890",
    "Address": "123 شارع النيل، القاهرة، مصر",
    "DOB": "1990-05-15",
    "Gender": "ذكر",
    "communicationType": "كلاهما",
    "profilePicture": "profile_001.jpg"
}

###

### Customer Signup (English)
POST http://localhost:3000/api/customers/signup
Content-Type: application/json

{
    "name": "<PERSON>",
    "username": "sara456",
    "email": "<EMAIL>",
    "password": "password456",
    "phone": "+201987654321",
    "Address": "456 Main Street, Cairo, Egypt",
    "DOB": "1995-08-20",
    "Gender": "female",
    "communicationType": "email",
    "profilePicture": "profile_002.jpg"
}
