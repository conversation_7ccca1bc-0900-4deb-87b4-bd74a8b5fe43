### Update Profile Picture
PUT http://localhost:3000/api/customers/profile/picture
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="profilePicture"; filename="new-profile.jpg"
Content-Type: image/jpeg

< ./test-images/new-profile.jpg
------WebKitFormBoundary7MA4YWxkTrZu0gW--

###

### Get Profile Picture URL
GET http://localhost:3000/api/customers/profile
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

###

### Access Profile Picture Directly
GET http://localhost:3000/api/uploads/profile-pictures/profile-1234567890-123456789.jpg
Content-Type: image/jpeg
