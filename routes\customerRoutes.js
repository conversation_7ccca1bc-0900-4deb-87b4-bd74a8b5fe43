const express = require('express');
const router = express.Router();
const customerController = require('../controller/customerController');
const { validatePostUser, validateLogin } = require('../validation/signup');
const authMiddleware = require('../middelwares/authorization');

// Customer Registration
router.post('/signup', validatePostUser(), customerController.signup);

// Customer Login
router.post('/signin', validateLogin(), customerController.login);

// Get Customer Profile (Protected Route)
router.get('/profile', authMiddleware.auth, customerController.getProfile);

module.exports = router;
