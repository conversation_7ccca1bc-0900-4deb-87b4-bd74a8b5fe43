/**
 * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import type { ValidationOptions } from './types';
export declare const errorMessage: (option: string, received: unknown, defaultValue: unknown, options: ValidationOptions, path?: string[] | undefined) => void;
