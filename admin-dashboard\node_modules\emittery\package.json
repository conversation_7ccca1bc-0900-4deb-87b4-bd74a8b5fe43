{"name": "emittery", "version": "0.8.1", "description": "Simple and modern async event emitter", "license": "MIT", "repository": "sindresorhus/emittery", "funding": "https://github.com/sindresorhus/emittery?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && nyc ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "devDependencies": {"@types/node": "^13.7.5", "ava": "^2.4.0", "delay": "^4.3.0", "nyc": "^15.0.0", "p-event": "^4.1.0", "tsd": "^0.14.0", "xo": "^0.36.1"}, "nyc": {"reporter": ["html", "lcov", "text"]}}