const mongoose = require('mongoose');

const cardSchema = new mongoose.Schema({
  customerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Customer',
    required: true
  },
  cardNumber: {
    type: String,
    required: true,
    trim: true,
    validate: {
      validator: function(v) {
        // Remove spaces and check if it's 16 digits
        return /^\d{16}$/.test(v.replace(/\s/g, ''));
      },
      message: 'Card number must be 16 digits'
    }
  },
  cardHolderName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  expiryMonth: {
    type: String,
    required: true,
    validate: {
      validator: function(v) {
        return /^(0[1-9]|1[0-2])$/.test(v);
      },
      message: 'Expiry month must be between 01 and 12'
    }
  },
  expiryYear: {
    type: String,
    required: true,
    validate: {
      validator: function(v) {
        const currentYear = new Date().getFullYear();
        const year = parseInt('20' + v);
        return /^\d{2}$/.test(v) && year >= currentYear && year <= currentYear + 20;
      },
      message: 'Expiry year must be valid and not expired'
    }
  },
  cvv: {
    type: String,
    required: true,
    validate: {
      validator: function(v) {
        return /^\d{3,4}$/.test(v);
      },
      message: 'CVV must be 3 or 4 digits'
    }
  },
  cardType: {
    type: String,
    enum: ['visa', 'mastercard', 'amex', 'discover'],
    required: true
  },
  isDefault: {
    type: Boolean,
    default: false
  },
  isActive: {
    type: Boolean,
    default: true
  },
  billingAddress: {
    street: {
      type: String,
      required: true,
      trim: true,
      maxlength: 200
    },
    city: {
      type: String,
      required: true,
      trim: true,
      maxlength: 100
    },
    state: {
      type: String,
      required: true,
      trim: true,
      maxlength: 100
    },
    zipCode: {
      type: String,
      required: true,
      trim: true,
      maxlength: 20
    },
    country: {
      type: String,
      required: true,
      trim: true,
      maxlength: 100,
      default: 'Egypt'
    }
  }
}, {
  timestamps: true
});

// Index for faster queries
cardSchema.index({ customerId: 1 });
cardSchema.index({ cardNumber: 1 });
cardSchema.index({ isDefault: 1 });

// Virtual for masked card number
cardSchema.virtual('maskedCardNumber').get(function() {
  if (this.cardNumber) {
    const cleaned = this.cardNumber.replace(/\s/g, '');
    return '**** **** **** ' + cleaned.slice(-4);
  }
  return '';
});

// Ensure only one default card per customer
cardSchema.pre('save', async function(next) {
  if (this.isDefault && this.isModified('isDefault')) {
    await this.constructor.updateMany(
      { customerId: this.customerId, _id: { $ne: this._id } },
      { isDefault: false }
    );
  }
  next();
});

// Method to detect card type from number
cardSchema.methods.detectCardType = function() {
  const number = this.cardNumber.replace(/\s/g, '');
  
  if (/^4/.test(number)) {
    return 'visa';
  } else if (/^5[1-5]/.test(number) || /^2[2-7]/.test(number)) {
    return 'mastercard';
  } else if (/^3[47]/.test(number)) {
    return 'amex';
  } else if (/^6/.test(number)) {
    return 'discover';
  }
  
  return 'unknown';
};

module.exports = mongoose.model('Card', cardSchema);
