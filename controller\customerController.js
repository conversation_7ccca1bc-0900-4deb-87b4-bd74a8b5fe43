const { validationResult } = require('express-validator');
const authService = require('../services/auth');

// Customer Signup
module.exports.signup = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { name, username, email, password, phone, Address, DOB, Gender, communicationType, profilePicture } = req.body;

    // Check if user already exists
    const userExists = await authService.doesUserExist(username, email);
    if (userExists) {
      return res.status(409).json({
        success: false,
        message: 'User with this username or email already exists'
      });
    }

    // Check if email already exists
    const emailExists = await authService.doesEmailExist(email);
    if (emailExists) {
      return res.status(409).json({
        success: false,
        message: 'Email already registered'
      });
    }

    // Create new user
    const customerInfo = {
      name,
      username,
      email,
      password,
      phone,
      Address,
      DOB,
      Gender,
      communicationType,
      profilePicture
    };

    const newUser = await authService.createUser(customerInfo);

    // Generate JWT token
    const token = authService.generateCJWT(newUser);

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user: {
          id: newUser._id,
          name: newUser.name,
          username: newUser.username,
          email: newUser.email,
          phone: newUser.phone,
          Address: newUser.Address,
          DOB: newUser.DOB,
          Gender: newUser.Gender,
          communicationType: newUser.communicationType
        },
        token
      }
    });

  } catch (error) {
    console.error('Signup error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

// Customer Login
module.exports.login = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { username, password } = req.body;

    // Check credentials
    const customer = await authService.checkCredentials(username, password);
    
    if (!customer) {
      return res.status(401).json({
        success: false,
        message: 'Invalid username or password'
      });
    }

    // Generate JWT token
    const token = authService.generateCJWT(customer);

    res.status(200).json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: customer._id,
          name: customer.name,
          username: customer.username,
          email: customer.email,
          phone: customer.phone,
          Address: customer.Address,
          DOB: customer.DOB,
          Gender: customer.Gender,
          communicationType: customer.communicationType
        },
        token
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

// Get Customer Profile
module.exports.getProfile = async (req, res) => {
  try {
    const customerId = req.user.Customer_id;
    const CustomerModel = require('../model/Customers');
    
    const customer = await CustomerModel.findById(customerId).select('-password');
    
    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Profile retrieved successfully',
      data: {
        user: customer
      }
    });

  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};
