### Step 1: Register a new customer (Parent) - No referral code
POST http://localhost:3000/api/customers/signup
Content-Type: application/json

{
    "name": "<PERSON> child",
    "username": "ahmed_child2",
    "email": "<EMAIL>",
    "password": "password123",
    "phone": "+966501234567",
    "Address": "123 Main Street, Riyadh, Saudi Arabia",
    "DOB": "1990-05-15",
    "Gender": "male",
    "communicationType": "both",
    "referredBy": 965167
}

###

### Step 2: Login as parent to get JWT token
POST http://localhost:3000/api/customers/signin
Content-Type: application/json

{
    "username": "ahmed_parent1",
    "password": "password123"
}

###

### Step 3: Get parent's referral info (Use token from Step 2)
GET http://localhost:3000/api/referral/info
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJDdXN0b21lcl9pZCI6IjY4NzQwMGVhMjkxY2M1ZTE0NzAzN2VjYyIsInVzZXJuYW1lIjoiYWhtZWRfcGFyZW50MSIsIlR5cGUiOiJDdXN0b21lciIsImlhdCI6MTc1MjQzMzI0OCwiZXhwIjoxNzUyNDM2ODQ4fQ.63Or62i9bI90KjDfsL_6Rfo2fmx13Qo9_lG64i6F9yM
Content-Type: application/json

###

### Step 4: Validate referral code (Public endpoint - use reference number from Step 3)
POST http://localhost:3000/api/referral/validate
Content-Type: application/json

{
    "referenceNumber": "REPLACE_WITH_REFERENCE_NUMBER_FROM_STEP_3"
}

###

### Step 5: Register a new customer (Child) with referral code
POST http://localhost:3000/api/customers/signup
Content-Type: application/json

{
    "name": "Sara Child",
    "username": "sara_child",
    "email": "<EMAIL>",
    "password": "password123",
    "phone": "+966501234568",
    "Address": "456 Second Street, Jeddah, Saudi Arabia",
    "DOB": "1995-08-20",
    "Gender": "female",
    "communicationType": "email",
    "referredBy": "REPLACE_WITH_REFERENCE_NUMBER_FROM_STEP_3"
}

###

### Step 6: Login as child to get JWT token
POST http://localhost:3000/api/customers/signin
Content-Type: application/json

{
    "username": "sara_child",
    "password": "password123"
}

###

### Step 7: Get child's referral info (Use token from Step 6)
GET http://localhost:3000/api/referral/info
Authorization: Bearer REPLACE_WITH_TOKEN_FROM_STEP_6
Content-Type: application/json

###

### Step 8: Get parent's referral network (Use token from Step 2)
GET http://localhost:3000/api/referral/network
Authorization: Bearer REPLACE_WITH_TOKEN_FROM_STEP_2
Content-Type: application/json

###

### Step 9: Get parent's referral network with pagination
GET http://localhost:3000/api/referral/network?page=1&limit=5
Authorization: Bearer REPLACE_WITH_TOKEN_FROM_STEP_2
Content-Type: application/json

###

### Step 10: Get parent's referral statistics
GET http://localhost:3000/api/referral/stats
Authorization: Bearer REPLACE_WITH_TOKEN_FROM_STEP_2
Content-Type: application/json

###

### Step 11: Register another child (Grandchild) using child's reference number
POST http://localhost:3000/api/customers/signup
Content-Type: application/json

{
    "name": "Omar Grandchild",
    "username": "omar_grandchild",
    "email": "<EMAIL>",
    "password": "password123",
    "phone": "+966501234569",
    "Address": "789 Third Street, Dammam, Saudi Arabia",
    "DOB": "1998-12-10",
    "Gender": "male",
    "communicationType": "phone",
    "referredBy": "REPLACE_WITH_CHILD_REFERENCE_NUMBER_FROM_STEP_7"
}

###

### Step 12: Test invalid referral code
POST http://localhost:3000/api/referral/validate
Content-Type: application/json

{
    "referenceNumber": "999999"
}

###

### Step 13: Test invalid referral code format
POST http://localhost:3000/api/referral/validate
Content-Type: application/json

{
    "referenceNumber": "12345"
}
