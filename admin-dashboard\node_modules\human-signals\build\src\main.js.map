{"version": 3, "sources": ["../../src/main.js"], "names": ["getSignalsByName", "signals", "reduce", "getSignalByName", "signalByNameMemo", "name", "number", "description", "supported", "action", "forced", "standard", "signalsByName", "getSignalsByNumber", "length", "SIGRTMAX", "signalsA", "Array", "from", "value", "getSignalByNumber", "Object", "assign", "signal", "findSignalByNumber", "undefined", "find", "constants", "signalA", "signalsByNumber"], "mappings": "2HAAA;;AAEA;AACA;;;;AAIA,KAAMA,CAAAA,gBAAgB,CAAG,UAAW;AAClC,KAAMC,CAAAA,OAAO,CAAG,yBAAhB;AACA,MAAOA,CAAAA,OAAO,CAACC,MAAR,CAAeC,eAAf,CAAgC,EAAhC,CAAP;AACD,CAHD;;AAKA,KAAMA,CAAAA,eAAe,CAAG;AACtBC,gBADsB;AAEtB,CAAEC,IAAF,CAAQC,MAAR,CAAgBC,WAAhB,CAA6BC,SAA7B,CAAwCC,MAAxC,CAAgDC,MAAhD,CAAwDC,QAAxD,CAFsB;AAGtB;AACA,MAAO;AACL,GAAGP,gBADE;AAEL,CAACC,IAAD,EAAQ,CAAEA,IAAF,CAAQC,MAAR,CAAgBC,WAAhB,CAA6BC,SAA7B,CAAwCC,MAAxC,CAAgDC,MAAhD,CAAwDC,QAAxD,CAFH,CAAP;;AAID,CARD;;AAUO,KAAMC,CAAAA,aAAa,CAAGZ,gBAAgB,EAAtC,C;;;;;AAKP,KAAMa,CAAAA,kBAAkB,CAAG,UAAW;AACpC,KAAMZ,CAAAA,OAAO,CAAG,yBAAhB;AACA,KAAMa,CAAAA,MAAM,CAAGC,mBAAW,CAA1B;AACA,KAAMC,CAAAA,QAAQ,CAAGC,KAAK,CAACC,IAAN,CAAW,CAAEJ,MAAF,CAAX,CAAuB,CAACK,KAAD,CAAQb,MAAR;AACtCc,iBAAiB,CAACd,MAAD,CAASL,OAAT,CADF,CAAjB;;AAGA,MAAOoB,CAAAA,MAAM,CAACC,MAAP,CAAc,EAAd,CAAkB,GAAGN,QAArB,CAAP;AACD,CAPD;;AASA,KAAMI,CAAAA,iBAAiB,CAAG,SAASd,MAAT,CAAiBL,OAAjB,CAA0B;AAClD,KAAMsB,CAAAA,MAAM,CAAGC,kBAAkB,CAAClB,MAAD,CAASL,OAAT,CAAjC;;AAEA,GAAIsB,MAAM,GAAKE,SAAf,CAA0B;AACxB,MAAO,EAAP;AACD;;AAED,KAAM,CAAEpB,IAAF,CAAQE,WAAR,CAAqBC,SAArB,CAAgCC,MAAhC,CAAwCC,MAAxC,CAAgDC,QAAhD,EAA6DY,MAAnE;AACA,MAAO;AACL,CAACjB,MAAD,EAAU;AACRD,IADQ;AAERC,MAFQ;AAGRC,WAHQ;AAIRC,SAJQ;AAKRC,MALQ;AAMRC,MANQ;AAORC,QAPQ,CADL,CAAP;;;AAWD,CAnBD;;;;AAuBA,KAAMa,CAAAA,kBAAkB,CAAG,SAASlB,MAAT,CAAiBL,OAAjB,CAA0B;AACnD,KAAMsB,CAAAA,MAAM,CAAGtB,OAAO,CAACyB,IAAR,CAAa,CAAC,CAAErB,IAAF,CAAD,GAAcsB,cAAU1B,OAAV,CAAkBI,IAAlB,IAA4BC,MAAvD,CAAf;;AAEA,GAAIiB,MAAM,GAAKE,SAAf,CAA0B;AACxB,MAAOF,CAAAA,MAAP;AACD;;AAED,MAAOtB,CAAAA,OAAO,CAACyB,IAAR,CAAaE,OAAO,EAAIA,OAAO,CAACtB,MAAR,GAAmBA,MAA3C,CAAP;AACD,CARD;;AAUO,KAAMuB,CAAAA,eAAe,CAAGhB,kBAAkB,EAA1C,C", "sourcesContent": ["import { constants } from 'os'\n\nimport { getSignals } from './signals.js'\nimport { SIGRTMAX } from './realtime.js'\n\n// Retrieve `signalsByName`, an object mapping signal name to signal properties.\n// We make sure the object is sorted by `number`.\nconst getSignalsByName = function() {\n  const signals = getSignals()\n  return signals.reduce(getSignalByName, {})\n}\n\nconst getSignalByName = function(\n  signalByNameMemo,\n  { name, number, description, supported, action, forced, standard },\n) {\n  return {\n    ...signalByNameMemo,\n    [name]: { name, number, description, supported, action, forced, standard },\n  }\n}\n\nexport const signalsByName = getSignalsByName()\n\n// Retrieve `signalsByNumber`, an object mapping signal number to signal\n// properties.\n// We make sure the object is sorted by `number`.\nconst getSignalsByNumber = function() {\n  const signals = getSignals()\n  const length = SIGRTMAX + 1\n  const signalsA = Array.from({ length }, (value, number) =>\n    getSignalByNumber(number, signals),\n  )\n  return Object.assign({}, ...signalsA)\n}\n\nconst getSignalByNumber = function(number, signals) {\n  const signal = findSignalByNumber(number, signals)\n\n  if (signal === undefined) {\n    return {}\n  }\n\n  const { name, description, supported, action, forced, standard } = signal\n  return {\n    [number]: {\n      name,\n      number,\n      description,\n      supported,\n      action,\n      forced,\n      standard,\n    },\n  }\n}\n\n// Several signals might end up sharing the same number because of OS-specific\n// numbers, in which case those prevail.\nconst findSignalByNumber = function(number, signals) {\n  const signal = signals.find(({ name }) => constants.signals[name] === number)\n\n  if (signal !== undefined) {\n    return signal\n  }\n\n  return signals.find(signalA => signalA.number === number)\n}\n\nexport const signalsByNumber = getSignalsByNumber()\n"], "file": "src/main.js"}